# Easy Social Share

A comprehensive Flutter plugin that enables seamless sharing of text, images, files, and multimedia content to various social media platforms and messaging apps.

## Features

### Supported Platforms
- **WhatsApp**: Share text messages and files (single or multiple)
- **Telegram**: Share text messages and files (single or multiple)
- **Twitter**: Share text messages with optional file attachments
- **Instagram**:
  - Direct messages
  - Feed posts (single or multiple images)
  - Reels (videos)
  - Stories (with customizable backgrounds and stickers)
- **Facebook**:
  - Feed posts with hashtags and files
  - Stories (with customizable backgrounds and stickers)
- **TikTok**: Share content to TikTok status (Android) or posts (iOS)
- **Messenger**: Share text messages
- **SMS**: Share text messages and files (single or multiple)
- **System Share**: Use the native platform share dialog
- **Clipboard**: Copy text to clipboard

### Cross-Platform Features
- **Get Installed Apps**: Check which social media apps are installed on the device
- **Platform-specific APIs**: Optimized methods for Android and iOS with different capabilities

## Platform Support

| Platform | Supported | File Sharing | Multiple Files |
|----------|-----------|--------------|----------------|
| Android  | ✅        | ✅           | ✅             |
| iOS      | ✅        | ⚠️ Limited   | ❌             |

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  easy_social_share: ^0.3.2
```

Then run:

```bash
flutter pub get
```

## Quick Start

```dart
import 'package:easy_social_share/easy_social_share.dart';

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EasySocialShare easySocialShare = EasySocialShare();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: ElevatedButton(
            child: Text("Share to WhatsApp"),
            onPressed: () async {
              // Check if WhatsApp is installed
              Map<String, bool> apps = await easySocialShare.getInstalledApps();

              if (apps['whatsapp'] == true) {
                // Share to WhatsApp (Android)
                await easySocialShare.android.shareToWhatsapp(
                  "Hello from Easy Social Share!",
                  "/path/to/image.jpg"
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
```

## Usage

### Basic Setup

```dart
import 'package:easy_social_share/easy_social_share.dart';

EasySocialShare easySocialShare = EasySocialShare();
```

### Check Installed Apps

```dart
Map<String, bool> apps = await easySocialShare.getInstalledApps();
print('WhatsApp installed: ${apps['whatsapp']}');
print('Instagram installed: ${apps['instagram']}');
```

### Platform-Specific Usage

The plugin provides platform-specific APIs to handle differences between Android and iOS:

#### Android Usage
Access methods via `easySocialShare.android.methodName()`

```dart
// Share to WhatsApp with image
await easySocialShare.android.shareToWhatsapp(
  "Check out this image!",
  "/path/to/image.jpg"
);

// Share multiple files to WhatsApp
await easySocialShare.android.shareFilesToWhatsapp([
  "/path/to/image1.jpg",
  "/path/to/image2.jpg",
]);

// Share to Instagram Story with custom background
await easySocialShare.android.shareToInstagramStory(
  'your_app_id',
  backgroundImage: '/path/to/background.jpg',
  stickerImage: '/path/to/sticker.png',
  backgroundTopColor: '#FF0000',
  backgroundBottomColor: '#00FF00',
);
```

#### iOS Usage
Access methods via `easySocialShare.iOS.methodName()`

```dart
// Share text to WhatsApp
await easySocialShare.iOS.shareToWhatsapp("Hello!");

// Share image to WhatsApp (separate method for iOS)
await easySocialShare.iOS.shareImageToWhatsApp("/path/to/image.jpg");

// Share to system dialog with multiple files
await easySocialShare.iOS.shareToSystem(
  "Check out these files!",
  filePaths: ["/path/to/file1.jpg", "/path/to/file2.pdf"]
);
```

## API Reference

### Cross-Platform Methods

| Method | Description | Returns |
|--------|-------------|---------|
| `getInstalledApps()` | Check which social apps are installed | `Future<Map<String, bool>>` |

### Android-Specific Methods

| Method | Parameters | Description | Multiple Files Support |
|--------|------------|-------------|----------------------|
| `shareToWhatsapp(message, filePath?)` | String message, String? filePath | Share to WhatsApp | ✅ `shareFilesToWhatsapp(List<String>)` |
| `shareToTelegram(message, filePath?)` | String message, String? filePath | Share to Telegram | ✅ `shareFilesToTelegram(List<String>)` |
| `shareToTwitter(message, filePath?)` | String message, String? filePath | Share to Twitter | ❌ |
| `shareToInstagramDirect(message)` | String message | Share to Instagram Direct | ❌ |
| `shareToInstagramFeed(message, filePath?)` | String message, String? filePath | Share to Instagram Feed | ✅ `shareFilesToInstagramFeed(List<String>)` |
| `shareToInstagramReels(videoPaths)` | List<String> videoPaths | Share videos to Instagram Reels | ❌ |
| `shareToInstagramStory(appId, options)` | String appId + story options* | Share to Instagram Story | ❌ |
| `shareToFacebook(hashtag, filePaths)` | String hashtag, List<String> filePaths | Share to Facebook | ❌ |
| `shareToFacebookStory(appId, options)` | String appId + story options* | Share to Facebook Story | ❌ |
| `shareToTiktokStatus(filePaths)` | List<String> filePaths | Share to TikTok Status | ❌ |
| `shareToMessenger(message)` | String message | Share to Messenger | ❌ |
| `shareToSMS(message, filePath?)` | String message, String? filePath | Share via SMS | ✅ `shareFilesToSMS(List<String>)` |
| `shareToSystem(title, message, filePath?)` | String title, String message, String? filePath | Share via system dialog | ✅ `shareFilesToSystem(String, List<String>)` |
| `copyToClipBoard(message)` | String message | Copy to clipboard | ❌ |

*Story options: `stickerImage`, `backgroundImage`, `backgroundVideo`, `backgroundTopColor`, `backgroundBottomColor`, `attributionURL`

### iOS-Specific Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `shareToWhatsapp(message)` | String message | Share text to WhatsApp |
| `shareImageToWhatsApp(filePath)` | String filePath | Share image to WhatsApp |
| `shareToTelegram(message)` | String message | Share text to Telegram |
| `shareToTwitter(message, filePath?)` | String message, String? filePath | Share to Twitter |
| `shareToInstagramDirect(message)` | String message | Share to Instagram Direct |
| `shareToInstagramFeed(imagePath)` | String imagePath | Share image to Instagram Feed |
| `shareToInstagramReels(videoPath)` | String videoPath | Share video to Instagram Reels |
| `shareToInstagramStory(appId, options)` | String appId + story options* | Share to Instagram Story |
| `shareToFacebook(hashtag, filePaths)` | String hashtag, List<String> filePaths | Share to Facebook |
| `shareToFacebookStory(appId, options)` | String appId + story options* | Share to Facebook Story |
| `shareToTiktokPost(videoFile, redirectUrl, fileType)` | String videoFile, String redirectUrl, TiktokFileType fileType | Share to TikTok Post |
| `shareToMessenger(message)` | String message | Share to Messenger |
| `shareToSMS(message)` | String message | Share text via SMS |
| `shareToSystem(message, filePaths?)` | String message, List<String>? filePaths | Share via system dialog |
| `copyToClipBoard(message)` | String message | Copy to clipboard |

## Advanced Examples

### Instagram Story Sharing

```dart
// Share to Instagram Story with custom styling
await easySocialShare.android.shareToInstagramStory(
  'your_instagram_app_id', // Get this from Facebook Developer Console
  stickerImage: '/path/to/sticker.png',
  backgroundImage: '/path/to/background.jpg',
  backgroundTopColor: '#FF6B6B',
  backgroundBottomColor: '#4ECDC4',
  attributionURL: 'https://your-app.com',
);
```

### Multiple File Sharing (Android Only)

```dart
// Share multiple images to WhatsApp
await easySocialShare.android.shareFilesToWhatsapp([
  '/path/to/image1.jpg',
  '/path/to/image2.jpg',
  '/path/to/image3.jpg',
]);

// Share multiple files via system dialog
await easySocialShare.android.shareFilesToSystem(
  'Check out these files!',
  [
    '/path/to/document.pdf',
    '/path/to/image.jpg',
    '/path/to/video.mp4',
  ],
);
```

### Platform-Specific Conditional Sharing

```dart
import 'dart:io';

Future<void> shareContent(String message, String filePath) async {
  if (Platform.isAndroid) {
    // Android supports file sharing for most platforms
    await easySocialShare.android.shareToWhatsapp(message, filePath);
  } else if (Platform.isIOS) {
    // iOS requires separate methods for text and images
    await easySocialShare.iOS.shareToWhatsapp(message);
    if (filePath.isNotEmpty) {
      await easySocialShare.iOS.shareImageToWhatsApp(filePath);
    }
  }
}
```

## Platform Differences

### Android vs iOS Capabilities

| Feature | Android | iOS | Notes |
|---------|---------|-----|-------|
| File sharing | ✅ Most apps support files | ⚠️ Limited support | iOS has more restrictions |
| Multiple files | ✅ Supported | ❌ Not supported | Android only feature |
| TikTok Status | ✅ Supported | ❌ Not available | Android exclusive |
| TikTok Post | ❌ Not available | ✅ Supported | iOS exclusive |
| WhatsApp images | Combined method | Separate method required | Different API approaches |

### File Type Support

- **Images**: JPG, PNG, GIF (most platforms)
- **Videos**: MP4, MOV (Instagram Reels, TikTok, Stories)
- **Documents**: PDF, DOC, TXT (System Share, some messaging apps)
- **Multiple formats**: Supported on Android for compatible apps

## Requirements

- **Flutter**: >=3.32.4
- **Dart**: >=3.0.0 <4.0.0
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 11.0+

## Setup

### Android Setup

Add the following to your `android/app/src/main/AndroidManifest.xml`:

```xml
<queries>
    <package android:name="com.whatsapp" />
    <package android:name="com.instagram.android" />
    <package android:name="com.facebook.katana" />
    <package android:name="com.twitter.android" />
    <package android:name="org.telegram.messenger" />
    <package android:name="com.zhiliaoapp.musically" />
    <package android:name="com.facebook.orca" />
</queries>
```

### iOS Setup

For TikTok sharing on iOS, add to your `ios/Runner/Info.plist`:

```xml
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>whatsapp</string>
    <string>instagram</string>
    <string>instagram-stories</string>
    <string>fb</string>
    <string>fb-messenger</string>
    <string>twitter</string>
    <string>tg</string>
    <string>tiktokopensdk</string>
</array>
```

## Error Handling

```dart
try {
  String result = await easySocialShare.android.shareToWhatsapp(
    "Hello World!",
    "/path/to/image.jpg"
  );
  print('Share result: $result');
} catch (e) {
  print('Error sharing: $e');
  // Handle error appropriately
}
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Repository**: [https://github.com/tentram/flutter_packages](https://github.com/tentram/flutter_packages)
- **Issues**: [Report issues here](https://github.com/tentram/flutter_packages/issues)
- **Example**: Check the [example](example/) directory for a complete implementation

## Changelog

### Version 0.3.2
- Current stable version
- Support for major social media platforms
- Cross-platform compatibility
- Multiple file sharing on Android

For detailed changelog, see [CHANGELOG.md](CHANGELOG.md).
